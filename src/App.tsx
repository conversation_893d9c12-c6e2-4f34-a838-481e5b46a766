import { useState, useCallback, useMemo } from "react";
import { useTodos } from "./hooks/useTodos";
import { useEditState } from "./hooks/useEditState";
import { TodoList } from "./components/TodoList";
import { AddTodoForm } from "./components/AddTodoForm";
import { LoadingSpinner } from "./components/LoadingSpinner";
import { ErrorMessage } from "./components/ErrorMessage";

function App() {
  const {
    todos,
    loading,
    error,
    addNewTodo,
    toggleTodo,
    removeTodo,
    updateTodoItem,
    clearError,
  } = useTodos();

  const { editId, editTitle, setEditTitle, startEdit, cancelEdit } =
    useEditState();

  const [newTitle, setNewTitle] = useState("");

  const handleAdd = useCallback(async () => {
    const success = await addNewTodo(newTitle);
    if (success) {
      setNewTitle("");
    }
  }, [newTitle, addNewTodo]);

  const handleEdit = useCallback(async () => {
    if (editId === null) return;
    const success = await updateTodoItem(editId, editTitle);
    if (success) {
      cancelEdit();
    }
  }, [editId, editTitle, updateTodoItem, cancelEdit]);

  const incompleteTodos = useMemo(
    () => todos.filter((todo) => !todo.completed),
    [todos]
  );

  const completedTodos = useMemo(
    () => todos.filter((todo) => todo.completed),
    [todos]
  );

  // if (loading)
  //   return <LoadingSpinner size="lg" message="Loading your todos..." />;

  return (
    <div className="screen relative">
      <div className="border-container bg-surface relative">
        {loading && (
          <LoadingSpinner size="lg" message="Loading your todos..." />
        )}
        {error && <ErrorMessage message={error} onDismiss={clearError} />}

        <header className="w-full h-fit flex justify-between items-center">
          <h1 className="text-3xl font-bold text-text-primary mb-6">
            Todo List
          </h1>
          <AddTodoForm
            newTitle={newTitle}
            setNewTitle={setNewTitle}
            onAdd={handleAdd}
            isLoading={loading}
          />
        </header>

        <main className="w-full flex gap-4">
          <section
            className="border-container overflow-hidden"
            aria-labelledby="active-tasks-heading"
          >
            <h2
              id="active-tasks-heading"
              className="text-xl font-semibold mb-4 text-text-primary"
            >
              Active Tasks ({incompleteTodos.length})
            </h2>
            <TodoList
              todos={incompleteTodos}
              editId={editId}
              editTitle={editTitle}
              setEditTitle={setEditTitle}
              onToggle={toggleTodo}
              onDelete={removeTodo}
              onStartEdit={startEdit}
              onSaveEdit={handleEdit}
              onCancelEdit={cancelEdit}
              showEditControls={true}
            />
          </section>
          <section
            className="border-container"
            aria-labelledby="completed-tasks-heading"
          >
            <h2
              id="completed-tasks-heading"
              className="text-xl font-semibold mb-4 text-text-primary"
            >
              Completed Tasks ({completedTodos.length})
            </h2>
            <TodoList
              todos={completedTodos}
              editId={editId}
              editTitle={editTitle}
              setEditTitle={setEditTitle}
              onToggle={toggleTodo}
              onDelete={removeTodo}
              onStartEdit={startEdit}
              onSaveEdit={handleEdit}
              onCancelEdit={cancelEdit}
              showEditControls={false}
            />
          </section>
        </main>
      </div>
    </div>
  );
}

export default App;
