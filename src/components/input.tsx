import { memo } from "react";
import cn from "clsx";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  title: string;
  setTitle: (title: string) => void;
  error?: string;
  maxLength?: number;
}

export const Input = memo(function Input({
  title,
  setTitle,
  error,
  maxLength = 100,
  className,
  ...inputProps
}: Readonly<InputProps>) {
  const hasError = <PERSON><PERSON><PERSON>(error);
  const isAtLimit = title.length >= maxLength;

  return (
    <div className="flex flex-col gap-1 mb-6">
      <input
        value={title}
        onChange={(e) => {
          if (e.target.value.length <= maxLength) {
            setTitle(e.target.value);
          }
        }}
        className={cn(
          "flex-1 px-3 py-2 border rounded-md focus:outline-none focus:ring-2",
          hasError
            ? "border-red-300 focus:ring-red-500"
            : "border-gray-300 focus:ring-primary",
          isAtLimit && "border-yellow-300",
          className
        )}
        {...inputProps}
      />
      <div className="flex justify-between text-xs">
        {error && <span className="text-red-500">{error}</span>}
        <span
          className={cn(
            "ml-auto",
            isAtLimit ? "text-yellow-600" : "text-gray-400"
          )}
        >
          {title.length}/{maxLength}
        </span>
      </div>
    </div>
  );
});
