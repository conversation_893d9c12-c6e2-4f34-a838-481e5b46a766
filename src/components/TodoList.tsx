import { memo } from "react";
import type { Todo } from "../type";
import { Task } from "./task";
import { Button } from "./button";

interface TodoListProps {
  todos: Todo[];
  editId: number | null;
  editTitle: string;
  setEditTitle: (title: string) => void;
  onToggle: (id: number) => void;
  onDelete: (id: number) => void;
  onStartEdit: (todo: Todo) => void;
  onSaveEdit: () => void;
  onCancelEdit: () => void;
  showEditControls?: boolean;
}

export const TodoList = memo(function TodoList({
  todos,
  editId,
  editTitle,
  setEditTitle,
  onToggle,
  onDelete,
  onStartEdit,
  onSaveEdit,
  onCancelEdit,
  showEditControls = true,
}: TodoListProps) {
  if (todos.length === 0) {
    return <p className="text-center text-gray-500 py-8">No todos yet</p>;
  }

  return (
    <ul aria-label="Todo items" className="space-y-1">
      {todos.map((todo) => (
        <div
          key={todo.id}
          className="flex items-center gap-2 p-2 rounded hover:bg-gray-50"
        >
          <Task
            isComplete={todo.completed}
            title={todo.title}
            editTitle={editTitle}
            setEditTitle={setEditTitle}
            handleComplete={() => onToggle(todo.id)}
            id={todo.id}
            editId={editId}
          />
          <div className="flex gap-2" aria-label={`Actions for ${todo.title}`}>
            {showEditControls && editId === todo.id ? (
              <>
                <Button onClick={onSaveEdit} aria-label="Save changes">
                  Save
                </Button>
                <Button onClick={onCancelEdit} aria-label="Cancel editing">
                  Cancel
                </Button>
              </>
            ) : (
              <>
                {showEditControls && (
                  <Button
                    onClick={() => onStartEdit(todo)}
                    aria-label={`Edit ${todo.title}`}
                  >
                    Edit
                  </Button>
                )}
                <Button
                  variant="tertiary"
                  onClick={() => onDelete(todo.id)}
                  aria-label={`Delete ${todo.title}`}
                >
                  Delete
                </Button>
              </>
            )}
          </div>
        </div>
      ))}
    </ul>
  );
});
