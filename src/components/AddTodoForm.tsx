import { memo, useCallback, useState } from "react";
import type { KeyboardEvent } from "react";
import { Input } from "./input";
import { Button } from "./button";

interface AddTodoFormProps {
  newTitle: string;
  setNewTitle: (title: string) => void;
  onAdd: () => void;
  isLoading?: boolean;
}

export const AddTodoForm = memo(function AddTodoForm({
  newTitle,
  setNewTitle,
  onAdd,
  isLoading = false,
}: AddTodoFormProps) {
  const [error, setError] = useState("");

  const validateAndAdd = useCallback(() => {
    const trimmedTitle = newTitle.trim();

    if (!trimmedTitle) {
      setError("Todo title cannot be empty");
      return;
    }

    if (trimmedTitle.length < 3) {
      setError("Todo title must be at least 3 characters");
      return;
    }

    setError("");
    onAdd();
  }, [newTitle, onAdd]);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter") {
        validateAndAdd();
      }
    },
    [validateAndAdd]
  );

  const handleTitleChange = useCallback(
    (title: string) => {
      setNewTitle(title);
      if (error) setError("");
    },
    [setNewTitle, error]
  );

  const isDisabled = isLoading;

  return (
    <div className="flex h-fit gap-2 items-start p-2 rounded-lg">
      <Input
        title={newTitle}
        setTitle={handleTitleChange}
        placeholder="minimum 3 characters"
        onKeyDown={handleKeyDown}
        error={error}
        maxLength={100}
        disabled={isLoading}
      />
      <Button
        className="font-semibold !text-xl h-fit w-24 mt-1"
        onClick={validateAndAdd}
        variant="accent"
        disabled={isDisabled}
      >
        Add
      </Button>
    </div>
  );
});
