import cn from "clsx";
import { memo } from "react";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "accent" | "tertiary" | "disabled";
  className?: string;
}

const baseClasses = "px-4 rounded-full font-medium text-sm cursor-pointer";

const variantClasses: Record<NonNullable<ButtonProps["variant"]>, string> = {
  primary: "bg-primary text-white hover:bg-primary/60 border border-primary",
  secondary:
    "bg-secondary text-text-secondary hover:bg-secondary/60 border border-secondary",
  accent: "bg-accent text-white hover:bg-accent/60 border border-accent",
  tertiary:
    "bg-transparent border border-primary text-primary hover:bg-primary/10",
  disabled: "bg-gray-200 text-gray-900 cursor-not-allowed",
};

export const Button = memo(function Button({
  variant = "primary",
  className,
  children,
  ...props
}: Readonly<ButtonProps>) {
  return (
    <button
      className={cn(baseClasses, variantClasses[variant], className)}
      {...props}
    >
      {children}
    </button>
  );
});
