import cn from "clsx";
import { memo } from "react";
import { Input } from "./input";

interface TaskProps {
  isComplete: boolean;
  title: string;
  editTitle?: string;
  setEditTitle?: (title: string) => void;
  handleComplete: (id: number) => void;
  id: number;
  editId?: number | null;
}

export const Task = memo(function Task({
  isComplete,
  title,
  editTitle,
  setEditTitle,
  handleComplete,
  id,
  editId,
}: Readonly<TaskProps>) {
  const isEditing = editId === id;
  const taskId = `task-${id}`;
  const checkboxId = `checkbox-${id}`;

  return (
    <li
      className="flex gap-2 items-center p-2 rounded hover:bg-gray-50 focus-within:bg-gray-50"
      aria-labelledby={taskId}
    >
      <input
        id={checkboxId}
        type="checkbox"
        checked={isComplete}
        className="w-4 h-4 cursor-pointer text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-2"
        onChange={() => handleComplete(id)}
        aria-describedby={taskId}
        aria-label={`Mark "${title}" as ${
          isComplete ? "incomplete" : "complete"
        }`}
      />
      <span
        className={cn({
          "line-through": isComplete,
          "opacity-50": isComplete,
        })}
      >
        {isEditing ? (
          <Input
            title={editTitle ?? ""}
            setTitle={setEditTitle ?? (() => {})}
            aria-label={`Edit todo: ${title}`}
            autoFocus
          />
        ) : (
          <label
            id={taskId}
            htmlFor={checkboxId}
            className="cursor-pointer select-none"
          >
            {title}
          </label>
        )}
      </span>
    </li>
  );
});
