import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import App from '../App'
import * as api from '../api'

// Mock the API module
vi.mock('../api', () => ({
  fetchTodos: vi.fn(),
  addTodo: vi.fn(),
  updateTodo: vi.fn(),
  deleteTodo: vi.fn(),
}))

const mockApi = api as any

describe('App Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockApi.fetchTodos.mockResolvedValue([
      { id: 1, title: 'Learn React', completed: false },
      { id: 2, title: 'Build todo app', completed: true },
    ])
  })

  it('renders the app and loads todos', async () => {
    render(<App />)
    
    // Should show loading initially
    expect(screen.getByText(/loading your todos/i)).toBeInTheDocument()
    
    // Wait for todos to load
    await waitFor(() => {
      expect(screen.getByText('Todo List')).toBeInTheDocument()
    })
    
    // Should show the todos
    expect(screen.getByText('Learn React')).toBeInTheDocument()
    expect(screen.getByText('Build todo app')).toBeInTheDocument()
    
    // Should show correct counts
    expect(screen.getByText('Active Tasks (1)')).toBeInTheDocument()
    expect(screen.getByText('Completed Tasks (1)')).toBeInTheDocument()
  })

  it('handles API error gracefully', async () => {
    mockApi.fetchTodos.mockRejectedValue(new Error('Network error'))
    
    render(<App />)
    
    await waitFor(() => {
      expect(screen.getByText(/failed to load todos/i)).toBeInTheDocument()
    })
  })

  it('adds a new todo', async () => {
    const user = userEvent.setup()
    const newTodo = { id: 3, title: 'New task', completed: false }
    mockApi.addTodo.mockResolvedValue(newTodo)
    
    render(<App />)
    
    await waitFor(() => {
      expect(screen.getByText('Todo List')).toBeInTheDocument()
    })
    
    // Add a new todo
    const input = screen.getByPlaceholderText(/add new todo/i)
    await user.type(input, 'New task')
    
    const addButton = screen.getByRole('button', { name: /add/i })
    await user.click(addButton)
    
    await waitFor(() => {
      expect(screen.getByText('New task')).toBeInTheDocument()
    })
    
    expect(mockApi.addTodo).toHaveBeenCalledWith({
      title: 'New task',
      completed: false,
    })
  })

  it('toggles todo completion', async () => {
    const user = userEvent.setup()
    mockApi.updateTodo.mockResolvedValue({ id: 1, title: 'Learn React', completed: true })
    
    render(<App />)
    
    await waitFor(() => {
      expect(screen.getByText('Learn React')).toBeInTheDocument()
    })
    
    // Find and click the checkbox for "Learn React"
    const checkbox = screen.getByLabelText(/mark "learn react" as complete/i)
    await user.click(checkbox)
    
    // Should move to completed section
    await waitFor(() => {
      expect(screen.getByText('Active Tasks (0)')).toBeInTheDocument()
      expect(screen.getByText('Completed Tasks (2)')).toBeInTheDocument()
    })
  })

  it('edits a todo', async () => {
    const user = userEvent.setup()
    mockApi.updateTodo.mockResolvedValue({ id: 1, title: 'Learn React Advanced', completed: false })
    
    render(<App />)
    
    await waitFor(() => {
      expect(screen.getByText('Learn React')).toBeInTheDocument()
    })
    
    // Click edit button
    const editButton = screen.getByLabelText(/edit learn react/i)
    await user.click(editButton)
    
    // Should show input field
    const editInput = screen.getByDisplayValue('Learn React')
    expect(editInput).toBeInTheDocument()
    
    // Edit the title
    await user.clear(editInput)
    await user.type(editInput, 'Learn React Advanced')
    
    // Save changes
    const saveButton = screen.getByRole('button', { name: /save/i })
    await user.click(saveButton)
    
    await waitFor(() => {
      expect(screen.getByText('Learn React Advanced')).toBeInTheDocument()
    })
  })

  it('deletes a todo', async () => {
    const user = userEvent.setup()
    mockApi.deleteTodo.mockResolvedValue(undefined)
    
    render(<App />)
    
    await waitFor(() => {
      expect(screen.getByText('Learn React')).toBeInTheDocument()
    })
    
    // Click delete button
    const deleteButton = screen.getByLabelText(/delete learn react/i)
    await user.click(deleteButton)
    
    await waitFor(() => {
      expect(screen.queryByText('Learn React')).not.toBeInTheDocument()
      expect(screen.getByText('Active Tasks (0)')).toBeInTheDocument()
    })
    
    expect(mockApi.deleteTodo).toHaveBeenCalledWith(1)
  })

  it('shows validation error for empty todo', async () => {
    const user = userEvent.setup()
    
    render(<App />)
    
    await waitFor(() => {
      expect(screen.getByText('Todo List')).toBeInTheDocument()
    })
    
    // Try to add empty todo
    const addButton = screen.getByRole('button', { name: /add/i })
    await user.click(addButton)
    
    expect(screen.getByText(/todo title cannot be empty/i)).toBeInTheDocument()
  })

  it('dismisses error messages', async () => {
    const user = userEvent.setup()
    mockApi.fetchTodos.mockRejectedValue(new Error('Network error'))
    
    render(<App />)
    
    await waitFor(() => {
      expect(screen.getByText(/failed to load todos/i)).toBeInTheDocument()
    })
    
    // Dismiss error
    const dismissButton = screen.getByLabelText(/×/i)
    await user.click(dismissButton)
    
    expect(screen.queryByText(/failed to load todos/i)).not.toBeInTheDocument()
  })
})
