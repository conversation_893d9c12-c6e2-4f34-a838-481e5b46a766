import { useEffect, useReducer, useState, useCallback } from "react";
import { todoReducer } from "../reducer";
import { fetchTodos, deleteTodo, updateTodo, addTodo } from "../api";
import type { Todo } from "../type";

export function useTodos() {
  const [todos, dispatch] = useReducer(todoReducer, []);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadTodos = async () => {
      try {
        const data = await fetchTodos();
        dispatch({ type: "SET_TODOS", payload: data });
      } catch (err) {
        console.error("Error fetching todos:", err);
        setError("Failed to fetch todos");
      } finally {
        setLoading(false);
      }
    };
    loadTodos();
  }, []);

  const addNewTodo = useCallback(async (title: string) => {
    if (!title.trim()) return false;
    setLoading(true);
    setError(null);

    const newTodo: Omit<Todo, "id"> = {
      title: title.trim(),
      completed: false,
    };

    try {
      const created = await addTodo(newTodo);
      dispatch({ type: "ADD_TODO", payload: created });
      return true;
    } catch (err) {
      console.error("Error adding todo:", err);
      setError("Failed to add todo");
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  const toggleTodo = useCallback(
    async (id: number) => {
      const todoToUpdate = todos.find((t) => t.id === id);
      if (!todoToUpdate) return false;

      dispatch({ type: "TOGGLE_TODO", payload: id });

      try {
        const updated = { ...todoToUpdate, completed: !todoToUpdate.completed };
        await updateTodo(updated);
        return true;
      } catch (err) {
        dispatch({ type: "TOGGLE_TODO", payload: id });
        console.error("Error toggling todo:", err);
        setError("Failed to update todo");
        return false;
      }
    },
    [todos]
  );

  const removeTodo = useCallback(
    async (id: number) => {
      const todoToDelete = todos.find((t) => t.id === id);
      if (!todoToDelete) return false;

      dispatch({ type: "DELETE_TODO", payload: id });
      setLoading(true);

      try {
        await deleteTodo(id);
        return true;
      } catch (err) {
        dispatch({ type: "ADD_TODO", payload: todoToDelete });
        console.error("Error deleting todo:", err);
        setError("Failed to delete todo");
        return false;
      } finally {
        setLoading(false);
      }
    },
    [todos]
  );

  const updateTodoItem = useCallback(
    async (id: number, title: string) => {
      const todoToUpdate = todos.find((t) => t.id === id);
      if (!todoToUpdate) return false;

      const updated: Todo = {
        id,
        title: title.trim(),
        completed: todoToUpdate.completed,
      };

      if (updated.title === todoToUpdate.title) return false;

      dispatch({ type: "UPDATE_TODO", payload: updated });
      setLoading(true);

      try {
        const result = await updateTodo(updated);
        dispatch({ type: "UPDATE_TODO", payload: result });
        return true;
      } catch (err) {
        dispatch({ type: "UPDATE_TODO", payload: todoToUpdate });
        console.error("Error updating todo:", err);
        setError("Failed to update todo");
        return false;
      } finally {
        setLoading(false);
      }
    },
    [todos]
  );

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    todos,
    loading,
    error,
    addNewTodo,
    toggleTodo,
    removeTodo,
    updateTodoItem,
    clearError,
  };
}
